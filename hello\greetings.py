"""
Greetings module - Contains various greeting functions.
"""

def say_hello(name: str = "World") -> None:
    """
    Print a personalized greeting.
    
    Args:
        name (str): The name to greet. Defaults to "World".
    """
    print(f"Hello, {name}! Nice to meet you!")

def say_goodbye(name: str = "World") -> None:
    """
    Print a personalized goodbye.
    
    Args:
        name (str): The name to say goodbye to. Defaults to "World".
    """
    print(f"Goodbye, {name}! See you later!")

def get_greeting(name: str = "World") -> str:
    """
    Return a greeting string.
    
    Args:
        name (str): The name to greet. Defaults to "World".
        
    Returns:
        str: A greeting message.
    """
    return f"Hello, {name}!"
